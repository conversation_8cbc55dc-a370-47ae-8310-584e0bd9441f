'use client';

import { ThemeProvider } from "@/components/shared/theme-toggle"
import { AuthProvider } from "@/lib/auth-context"
import { Toaster } from "sonner"
import { CoinbaseWalletProvider } from '@/lib/coinbase-wallet-provider'
import { RainbowKitProviders } from '@/app/providers/rainbowkit-provider'
import Head from 'next/head';
import "./globals.css"

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <Head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300..700&family=Orbitron:wght@400..900&family=Inter:wght@100..900&family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap" rel="stylesheet" />
      </Head>
      <body className="min-h-screen bg-background font-body antialiased">
        {/* Full page fixed background image */}
        <div
          className="absolute inset-0 -z-10 bg-cover bg-fixed bg-center"
          style={{ backgroundImage: "url('https://images.unsplash.com/photo-1518655048521-f130df041f66?q=80&w=2070&auto=format&fit=crop')" }} // Example futuristic/tech image
        />
        <RainbowKitProviders>
          <CoinbaseWalletProvider
            appName="Crefy Connect"
            darkMode={true}
          >
            <ThemeProvider
              attribute="class"
              defaultTheme="dark" // Forcing dark theme as per design aesthetic
              enableSystem={false} // System theme preference disabled to enforce dark theme
              disableTransitionOnChange
            >
              <AuthProvider>
                {children}
                <Toaster position="top-right" />
              </AuthProvider>
            </ThemeProvider>
          </CoinbaseWalletProvider>
        </RainbowKitProviders>
      </body>
    </html>
  )
}