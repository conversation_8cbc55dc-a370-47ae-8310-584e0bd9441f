"use client"

import React, { useEffect, useState } from "react"
import { useAuth } from "@/lib/auth-context"
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useRouter } from "next/navigation"
import { apiService } from "@/lib/api"
import { DashboardSidebar } from "@/components/shared/dashboard/sidebar"
import { ProfileDropdown } from "@/components/shared/profile-dropdown"

export default function ProfilePage() {
  const { user, token, refreshProfile } = useAuth()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [profileData, setProfileData] = useState<any>(null)
  const [applications, setApplications] = useState<any[]>([])
  const [error, setError] = useState<string | null>(null)
  
  // Fetch profile data and applications
  const fetchProfileData = async () => {
    if (!token) return

    try {
      setIsLoading(true)
      setError(null)

      // Fetch profile data
      const profileResponse = await apiService.getProfile(token)
      if (profileResponse.success && profileResponse.data) {
        setProfileData(profileResponse.data)
      } else {
        setError(profileResponse.error || "Failed to fetch profile data")
      }

      // Fetch applications for activity data
      const appsResponse = await apiService.getApplications(token)
      if (appsResponse.success && appsResponse.data) {
        setApplications(appsResponse.data)
      }
    } catch (error) {
      setError("Failed to fetch profile data")
      console.error('Error fetching profile:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Redirect if not authenticated
  useEffect(() => {
    if (!user || !token) {
      router.push("/auth/signin")
    } else {
      fetchProfileData()
    }
  }, [user, token, router])
  
  // Get initials for avatar
  const getInitials = (email: string) => {
    return email.split('@')[0].substring(0, 1).toUpperCase()
  }

  // Format date to be more readable
  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A"
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }
  
  return (
    <div className="flex min-h-screen bg-slate-50">
      <DashboardSidebar />
      
      <main className="flex-1 p-6 md:p-10">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-[#4B0082]">Profile</h1>
          <ProfileDropdown />
        </div>
        
        <div className="max-w-4xl mx-auto">
          {isLoading ? (
            <div className="bg-white/80 backdrop-blur-sm border border-gray-200 rounded-2xl shadow-xl p-12 flex flex-col items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#4B0082] mb-4"></div>
              <p className="text-gray-600">Loading profile...</p>
            </div>
          ) : error ? (
            <div className="bg-white/80 backdrop-blur-sm border border-gray-200 rounded-2xl shadow-xl p-12 flex flex-col items-center justify-center">
              <p className="text-red-600 font-semibold">{error}</p>
              <Button
                onClick={fetchProfileData}
                className="mt-4 bg-[#4B0082] text-white hover:bg-opacity-90"
              >
                Try Again
              </Button>
            </div>
          ) : (

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* User overview card */}
        <Card className="p-6 border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <div className="flex flex-col items-center text-center">
            <Avatar className="h-24 w-24 border-4 border-[#B497D6]/30">
              <AvatarFallback className="bg-gradient-to-br from-[#4B0082] to-[#B497D6] text-white font-bold text-3xl">
                {profileData?.email ? getInitials(profileData.email) : "U"}
              </AvatarFallback>
            </Avatar>

            <h2 className="mt-4 text-xl font-semibold">
              {profileData?.name || profileData?.email?.split('@')[0] || "User"}
            </h2>
            <p className="text-sm text-gray-500 mt-1">{profileData?.email}</p>

            <div className="mt-3 px-3 py-1 bg-[#B497D6]/10 rounded-full text-[#4B0082] font-medium">
              Developer
            </div>
            
            <div className="mt-6 w-full">
              <div className="flex justify-between text-sm mb-2">
                <span className="text-gray-500">Account ID:</span>
                <span className="font-medium font-mono text-xs">{profileData?.id || "N/A"}</span>
              </div>
              <div className="flex justify-between text-sm mb-2">
                <span className="text-gray-500">Created:</span>
                <span className="font-medium">{formatDate(profileData?.createdAt)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Status:</span>
                <span className={`font-medium ${profileData?.isActive ? 'text-green-600' : 'text-amber-600'}`}>
                  {profileData?.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
          </div>
        </Card>
        
        {/* User details card */}
        <Card className="p-6 col-span-1 md:col-span-2 border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-[#4B0082]">Account Details</h2>
          </div>

          <div className="space-y-6">
            <div>
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                value={profileData?.name || ""}
                disabled
                className="mt-1 bg-gray-50"
              />
            </div>

            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                value={profileData?.email || ""}
                disabled
                className="mt-1 bg-gray-50"
              />
            </div>

            <div>
              <Label htmlFor="id">Developer ID</Label>
              <Input
                id="id"
                value={profileData?.id || ""}
                disabled
                className="mt-1 bg-gray-50 font-mono text-sm"
              />
            </div>

            <div>
              <Label htmlFor="verification">Account Status</Label>
              <div className="mt-1 flex items-center">
                <div className={`h-3 w-3 rounded-full mr-2 ${profileData?.isActive ? "bg-green-500" : "bg-amber-500"}`}></div>
                <span>{profileData?.isActive ? "Active" : "Inactive"}</span>
              </div>
            </div>
          </div>
        </Card>
        
        {/* Activity and usage stats card */}
        <Card className="p-6 col-span-1 md:col-span-3 border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <h2 className="text-xl font-semibold text-[#4B0082] mb-6">Account Activity</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-[#B497D6]/10 rounded-xl">
              <h3 className="text-sm font-medium text-gray-500">Account Created</h3>
              <p className="text-lg font-semibold mt-1">{formatDate(profileData?.createdAt)}</p>
            </div>

            <div className="p-4 bg-[#B497D6]/10 rounded-xl">
              <h3 className="text-sm font-medium text-gray-500">Total Applications</h3>
              <p className="text-lg font-semibold mt-1">{applications.length}</p>
            </div>

            <div className="p-4 bg-[#B497D6]/10 rounded-xl">
              <h3 className="text-sm font-medium text-gray-500">Account Status</h3>
              <p className={`text-lg font-semibold mt-1 ${profileData?.isActive ? 'text-green-600' : 'text-amber-600'}`}>
                {profileData?.isActive ? 'Active' : 'Inactive'}
              </p>
            </div>
          </div>
          
          <div className="mt-6">
            <Button 
              variant="outline" 
              onClick={() => router.push("/dashboard/dashboard/api-keys")}
              className="border-[#B497D6]/30 hover:border-[#4B0082] hover:bg-[#B497D6]/10"
            >
              Manage API Keys
            </Button>
          </div>
        </Card>
      </div>
          )}
        </div>
      </main>
    </div>
  )
}
