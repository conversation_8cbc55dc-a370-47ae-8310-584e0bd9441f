"use client"
import React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"

import { useAuth } from "@/lib/auth-context"
import { LogOut } from "lucide-react"

export function ProfileDropdown() {
  const { logout, user } = useAuth()

  const handleLogout = () => {
    logout()
  }

  // Generate initials from email - get first letter of username
  const getInitials = (email: string) => {
    return email.split('@')[0].substring(0, 1).toUpperCase()
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-10 w-10 rounded-full hover:bg-[#B497D6]/10 transition-all duration-300">
          <Avatar className="h-10 w-10 border-2 border-[#B497D6]/20 hover:border-[#4B0082]/40 transition-all duration-300">
            <AvatarFallback className="bg-gradient-to-br from-[#4B0082] to-[#B497D6] text-white font-bold text-lg">
              {user?.email ? getInitials(user.email) : "U"}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-64 bg-white/95 backdrop-blur-sm border border-gray-200/50 shadow-2xl rounded-2xl" align="end" forceMount>
        <DropdownMenuLabel className="font-normal p-4">
          <div className="flex items-center space-x-3">
            <Avatar className="h-12 w-12 border-2 border-[#B497D6]/30">
              <AvatarFallback className="bg-gradient-to-br from-[#4B0082] to-[#B497D6] text-white font-bold text-xl">
                {user?.email ? getInitials(user.email) : "U"}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-semibold leading-none text-gray-900">
                {user?.email?.split('@')[0] || "User"}
              </p>
              <p className="text-xs leading-none text-gray-500">
                {user?.email || "<EMAIL>"}
              </p>
              <p className="text-xs leading-none text-[#4B0082] font-medium capitalize bg-[#B497D6]/10 px-2 py-1 rounded-full inline-block">
                {user?.role || "developer"}
              </p>
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-gray-200/50" />

        <DropdownMenuItem onClick={handleLogout} className="cursor-pointer hover:bg-red-50 focus:bg-red-50 text-red-600 rounded-lg mx-2 my-1">
          <LogOut className="mr-2 h-4 w-4" />
          <span>Log out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}